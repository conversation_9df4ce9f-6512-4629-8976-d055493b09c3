import React from 'react';

interface FlagIconProps {
  countryCode: string;
  className?: string;
}

const FlagIcon: React.FC<FlagIconProps> = ({ countryCode, className = "w-5 h-4 flag-icon" }) => {
  const flags = {
    BA: (
      <svg className={className} viewBox="0 0 20 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect width="20" height="16" fill="#002395"/>
        <polygon points="0,0 20,8 0,16" fill="#FFCD00"/>
        <g fill="white">
          <polygon points="2,2 4,4 2,6 0,4"/>
          <polygon points="2,6 4,8 2,10 0,8"/>
          <polygon points="2,10 4,12 2,14 0,12"/>
          <polygon points="6,4 8,6 6,8 4,6"/>
          <polygon points="6,8 8,10 6,12 4,10"/>
          <polygon points="10,6 12,8 10,10 8,8"/>
        </g>
      </svg>
    ),
    US: (
      <svg className={className} viewBox="0 0 20 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect width="20" height="16" fill="#B22234"/>
        <rect width="20" height="1.23" y="1.23" fill="white"/>
        <rect width="20" height="1.23" y="3.69" fill="white"/>
        <rect width="20" height="1.23" y="6.15" fill="white"/>
        <rect width="20" height="1.23" y="8.62" fill="white"/>
        <rect width="20" height="1.23" y="11.08" fill="white"/>
        <rect width="20" height="1.23" y="13.54" fill="white"/>
        <rect width="8" height="8.62" fill="#3C3B6E"/>
        <g fill="white">
          <circle cx="1.5" cy="1.5" r="0.3"/>
          <circle cx="3" cy="1.5" r="0.3"/>
          <circle cx="4.5" cy="1.5" r="0.3"/>
          <circle cx="6" cy="1.5" r="0.3"/>
          <circle cx="2.25" cy="2.5" r="0.3"/>
          <circle cx="3.75" cy="2.5" r="0.3"/>
          <circle cx="5.25" cy="2.5" r="0.3"/>
          <circle cx="1.5" cy="3.5" r="0.3"/>
          <circle cx="3" cy="3.5" r="0.3"/>
          <circle cx="4.5" cy="3.5" r="0.3"/>
          <circle cx="6" cy="3.5" r="0.3"/>
          <circle cx="2.25" cy="4.5" r="0.3"/>
          <circle cx="3.75" cy="4.5" r="0.3"/>
          <circle cx="5.25" cy="4.5" r="0.3"/>
          <circle cx="1.5" cy="5.5" r="0.3"/>
          <circle cx="3" cy="5.5" r="0.3"/>
          <circle cx="4.5" cy="5.5" r="0.3"/>
          <circle cx="6" cy="5.5" r="0.3"/>
          <circle cx="2.25" cy="6.5" r="0.3"/>
          <circle cx="3.75" cy="6.5" r="0.3"/>
          <circle cx="5.25" cy="6.5" r="0.3"/>
          <circle cx="1.5" cy="7.5" r="0.3"/>
          <circle cx="3" cy="7.5" r="0.3"/>
          <circle cx="4.5" cy="7.5" r="0.3"/>
          <circle cx="6" cy="7.5" r="0.3"/>
        </g>
      </svg>
    ),
    DE: (
      <svg className={className} viewBox="0 0 20 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect width="20" height="5.33" fill="#000000"/>
        <rect width="20" height="5.33" y="5.33" fill="#DD0000"/>
        <rect width="20" height="5.33" y="10.67" fill="#FFCE00"/>
      </svg>
    )
  };

  return flags[countryCode as keyof typeof flags] || null;
};

export default FlagIcon;
