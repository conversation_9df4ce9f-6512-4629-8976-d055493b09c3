'use client';

import { useTranslations } from 'next-intl';
import { useEffect } from 'react';
import { useInView } from 'react-intersection-observer';
import gradientGL from 'gradient-gl';
import Button from '@/components/ui/Button';

const CTASection = () => {
  const t = useTranslations('hero');

  useEffect(() => {
    // Initialize gradient-gl with the specified seed only on client
    if (typeof window !== 'undefined') {
      const timer = setTimeout(() => {
        gradientGL('a2.f10e', '#cta-gradient-bg');
      }, 100);

      return () => clearTimeout(timer);
    }
  }, []);

  // Intersection observers for staggered animations
  const { ref: titleRef, inView: titleInView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  const { ref: descriptionRef, inView: descriptionInView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  const { ref: buttonRef, inView: buttonInView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  return (
    <section className="py-32 relative overflow-hidden">
      {/* WebGL Gradient Background */}
      <div id="cta-gradient-bg" className="gradient-container absolute inset-0 z-0"></div>
      <div className="grain"></div>

      <div className="relative max-w-7xl mx-auto px-6 lg:px-8 z-10">
        <div className="flex flex-col lg:flex-row items-center justify-between gap-16">
          {/* Left side - Content */}
          <div className="flex-1 space-y-8">
            <h2
              ref={titleRef}
              className={`text-4xl lg:text-6xl font-bold text-black leading-tight transition-all duration-1000 ease-out delay-300 ${
                titleInView
                  ? 'opacity-100 translate-y-0'
                  : 'opacity-0 translate-y-8'
              }`}
            >
              Počnite ovdje
            </h2>

            <p
              ref={descriptionRef}
              className={`text-xl text-black/90 max-w-2xl leading-relaxed transition-all duration-1000 ease-out delay-600 ${
                descriptionInView
                  ? 'opacity-100 translate-y-0'
                  : 'opacity-0 translate-y-8'
              }`}
            >
              Prepustite papirologiju nama i uštedite vrijeme. Naš tim je spreman da vam pomogne sa svim administrativnim procedurama brzo, sigurno i bez komplikacija.
            </p>
          </div>

          {/* Right side - Button */}
          <div
            ref={buttonRef}
            className={`flex-shrink-0 transition-all duration-1000 ease-out delay-900 ${
              buttonInView
                ? 'opacity-100 translate-y-0'
                : 'opacity-0 translate-y-8'
            }`}
          >
            <Button variant="outline" size="lg">
              {t('contactButton')}
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CTASection;
