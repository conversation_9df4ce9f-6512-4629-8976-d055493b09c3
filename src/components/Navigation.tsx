'use client';

import { useTranslations, useLocale } from 'next-intl';
import { useRouter, usePathname } from 'next/navigation';
import { useState, useEffect } from 'react';
import Image from 'next/image';
import NavButton from '@/components/ui/NavButton';

const Navigation = () => {
  const t = useTranslations('nav');
  const locale = useLocale();
  const router = useRouter();
  const pathname = usePathname();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isLangOpen, setIsLangOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      setIsScrolled(scrollTop > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const languages = [
    { code: 'bs', name: '<PERSON><PERSON><PERSON>', flag: '🇧🇦' },
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'de', name: 'Deutsch', flag: '🇩🇪' }
  ];

  const currentLanguage = languages.find(lang => lang.code === locale);

  const switchLanguage = (newLocale: string) => {
    const segments = pathname.split('/');

    if (newLocale === 'bs') {
      // For Bosnian (default), remove the locale from the path
      if (segments[1] === 'en' || segments[1] === 'de') {
        // Remove the current locale segment
        segments.splice(1, 1);
        const newPath = segments.join('/') || '/';
        router.push(newPath);
      }
      // If already on Bosnian (no locale in path), no navigation needed
    } else {
      // For non-default locales (en, de), add/replace the locale in the path
      if (segments[1] === 'en' || segments[1] === 'de') {
        // Replace existing locale
        segments[1] = newLocale;
      } else {
        // Add locale to path (current path is default/bs)
        segments.splice(1, 0, newLocale);
      }
      const newPath = segments.join('/');
      router.push(newPath);
    }

    setIsLangOpen(false);
  };

  return (
    <nav className={`fixed w-full top-0 z-50 transition-all duration-500 animate-slide-in-nav ${
      isScrolled
        ? 'bg-black'
        : 'bg-transparent'
    }`}>
      <div className="max-w-7xl mx-auto px-6 lg:px-8">
        <div className={`relative flex items-center h-24`}>
          {/* Logo */}
          <div className="flex-shrink-0 flex items-center">
            <div className="w-12 h-12 relative">
              <Image
                src="/logo.svg"
                alt="AlbatrosDoc Logo"
                width={48}
                height={48}
                className="w-full h-full object-contain"
                style={{
                  filter: 'brightness(0) saturate(100%) invert(98%) sepia(8%) saturate(346%) hue-rotate(75deg) brightness(106%) contrast(96%)'
                }}
              />
            </div>
          </div>

          {/* Desktop Navigation - Absolutely centered */}
          <div className="hidden md:block absolute left-1/2 transform -translate-x-1/2">
            <div className="flex items-center space-x-12">
              <a href="#" className={`font-light text-lg transition-all duration-300 relative group ${
                isScrolled ? 'text-albatros-ivory hover:text-albatros-ivory' : 'text-albatros-ivory hover:text-albatros-ivory drop-shadow-lg'
              }`}>
                {t('home')}
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-white group-hover:w-full transition-all duration-300"></span>
              </a>
              <a href="#" className={`font-light text-lg transition-all duration-300 relative group text-albatros-ivory/90 ${
                isScrolled ? 'hover:text-albatros-ivory' : 'hover:text-albatros-ivory drop-shadow-lg'
              }`}>
                {t('about')}
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-white group-hover:w-full transition-all duration-300"></span>
              </a>
              <a href="#" className={`font-light text-lg transition-all duration-300 relative group text-albatros-ivory/90 ${
                isScrolled ? 'hover:text-albatros-ivory' : 'hover:text-albatros-ivory drop-shadow-lg'
              }`}>
                {t('services')}
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-white group-hover:w-full transition-all duration-300"></span>
              </a>
              <a href="#" className={`font-light text-lg transition-all duration-300 relative group text-albatros-ivory/90 ${
                isScrolled ? 'hover:text-albatros-ivory' : 'hover:text-albatros-ivory drop-shadow-lg'
              }`}>
                {t('contact')}
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-white group-hover:w-full transition-all duration-300"></span>
              </a>
            </div>
          </div>

          {/* Language Switcher & Mobile Menu Button - Right aligned */}
          <div className="flex items-center space-x-4 ml-auto">
            {/* Language Switcher */}
            <div className="relative">
              <NavButton
                variant="language"
                isScrolled={isScrolled}
                onClick={() => setIsLangOpen(!isLangOpen)}
              >
                <span className="text-lg">{currentLanguage?.flag}</span>
                <span className="hidden sm:block font-medium">{currentLanguage?.name}</span>
                <svg className="w-4 h-4 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </NavButton>

              {isLangOpen && (
                <div className={`absolute right-0 mt-3 w-auto backdrop-blur-xl rounded-2xl shadow-2xl py-2 z-50 border border-white/20 transition-all duration-500 animate-fade-in-short ${
                  isScrolled
                    ? 'bg-black'
                    : 'glass'
                }`}>
                  <div className="px-2 space-y-1">
                    {languages.map((lang) => (
                      <NavButton
                        key={lang.code}
                        variant="dropdown-item"
                        isActive={locale === lang.code}
                        onClick={() => switchLanguage(lang.code)}
                      >
                        <span className="text-lg">{lang.flag}</span>
                        <span className="font-medium">{lang.name}</span>
                      </NavButton>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Mobile menu button */}
            <div className="md:hidden">
              <NavButton
                variant="mobile"
                isScrolled={isScrolled}
                onClick={() => setIsMenuOpen(!isMenuOpen)}
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  {isMenuOpen ? (
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  ) : (
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                  )}
                </svg>
              </NavButton>
            </div>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden">
            <div className={`px-4 pt-4 pb-6 space-y-2 backdrop-blur-xl transition-all duration-300 ${
              isScrolled
                ? 'bg-slate-800/95'
                : 'bg-slate-900/90'
            }`}>
              <a href="#" className="text-white hover:text-blue-300 block px-4 py-3 text-base font-medium rounded-lg hover:bg-white/10 transition-all duration-300">
                {t('home')}
              </a>
              <a href="#" className="text-white/80 hover:text-white block px-4 py-3 text-base font-medium rounded-lg hover:bg-white/10 transition-all duration-300">
                {t('about')}
              </a>
              <a href="#" className="text-white/80 hover:text-white block px-4 py-3 text-base font-medium rounded-lg hover:bg-white/10 transition-all duration-300">
                {t('services')}
              </a>
              <a href="#" className="text-white/80 hover:text-white block px-4 py-3 text-base font-medium rounded-lg hover:bg-white/10 transition-all duration-300">
                {t('contact')}
              </a>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};

export default Navigation;
